/**
 * 统一媒体管理系统
 * 整合所有媒体管理组件，提供统一的接口
 */

import { MediaStateManager } from './MediaStateManager'
import { LiveStreamBackgroundManager } from './LiveStreamBackgroundManager'
import { ForegroundMediaPlayer } from './ForegroundMediaPlayer'
import { AudioStateManager } from './AudioStateManager'
import { ResourceManager } from './ResourceManager'

import type {
  MediaManagerConfig,
  MediaManagerCallbacks,
  MediaResource,
  MediaEvent,
  PreloadStrategy,
} from './types'

export interface UnifiedMediaSystemConfig {
  mediaManager?: Partial<MediaManagerConfig>
  preloadStrategy?: Partial<PreloadStrategy>
  audioConfig?: {
    ttsVolume?: number
    bgmVolume?: number
    videoVolume?: number
    fadeInDuration?: number
    fadeOutDuration?: number
    duckingLevel?: number
  }
  debugMode?: boolean
}

export interface UnifiedMediaSystemCallbacks extends MediaManagerCallbacks {
  // 直播背景回调
  onLiveVideoSwitch?: (fromIndex: number, toIndex: number) => void
  onLiveLoopComplete?: (loopCount: number) => void

  // 前景媒体回调
  onForegroundStart?: (resource: MediaResource) => void
  onForegroundEnd?: (resource: MediaResource) => void
  onForegroundSkip?: (resource: MediaResource, watchedDuration: number) => void

  // 音频回调
  onTTSStart?: (message: string) => void
  onTTSEnd?: (message: string) => void
  onBGMStart?: () => void
  onBGMEnd?: () => void

  // 资源回调
  onResourceLoaded?: (resource: MediaResource) => void
  onResourceCached?: (resource: MediaResource, cacheSize: number) => void
}

/**
 * 统一媒体管理系统
 * 提供完整的媒体播放、状态管理和资源优化功能
 */
export class UnifiedMediaSystem {
  // 核心管理器
  private mediaStateManager: MediaStateManager
  private liveBackgroundManager: LiveStreamBackgroundManager
  private foregroundPlayer: ForegroundMediaPlayer
  private audioManager: AudioStateManager
  private resourceManager: ResourceManager

  // 配置
  private config: UnifiedMediaSystemConfig
  private callbacks: UnifiedMediaSystemCallbacks

  constructor(
    config: UnifiedMediaSystemConfig = {},
    callbacks: UnifiedMediaSystemCallbacks = {},
  ) {
    this.config = config
    this.callbacks = callbacks

    // 初始化核心媒体状态管理器
    this.mediaStateManager = new MediaStateManager(config.mediaManager, {
      onStateChange: callbacks.onStateChange,
      onEventProcessed: callbacks.onEventProcessed,
      onError: callbacks.onError,
    })

    // 初始化直播背景管理器
    this.liveBackgroundManager = new LiveStreamBackgroundManager(
      this.mediaStateManager,
      {
        onVideoSwitch: callbacks.onLiveVideoSwitch,
        onLoopComplete: callbacks.onLiveLoopComplete,
        onError: callbacks.onError,
      },
    )

    // 初始化前景媒体播放器
    this.foregroundPlayer = new ForegroundMediaPlayer(this.mediaStateManager, {
      onMediaStart: callbacks.onForegroundStart,
      onMediaEnd: callbacks.onForegroundEnd,
      onMediaSkip: callbacks.onForegroundSkip,
      onError: callbacks.onError,
      onUserInteraction: () => {
        // 记录用户交互
        this.mediaStateManager.addEvent({
          type: 'user_interaction',
          priority: 0, // CRITICAL priority
          mediaType: 'foreground_video',
          data: { timestamp: Date.now() },
        })
      },
    })

    // 初始化音频管理器
    this.audioManager = new AudioStateManager(config.audioConfig, {
      onTTSStart: callbacks.onTTSStart,
      onTTSEnd: callbacks.onTTSEnd,
      onBGMStart: callbacks.onBGMStart,
      onBGMEnd: callbacks.onBGMEnd,
      onError: callbacks.onError,
    })

    // 初始化资源管理器
    this.resourceManager = new ResourceManager(config.preloadStrategy, {
      onResourceLoaded: callbacks.onResourceLoaded,
      onResourceCached: callbacks.onResourceCached,
      onError: callbacks.onError,
    })

    if (config.debugMode) {
      console.log('[VIDEO-GROUP-MSG] 🎬 UnifiedMediaSystem initialized')
    }
  }

  /**
   * 设置媒体元素引用
   */
  setMediaElements(elements: {
    primaryVideo: HTMLVideoElement
    secondaryVideo: HTMLVideoElement
    foregroundVideo: HTMLVideoElement
    foregroundImage: HTMLImageElement
    ttsAudio?: HTMLAudioElement
    bgmAudio?: HTMLAudioElement
  }): void {
    // 设置直播背景视频元素
    this.liveBackgroundManager.setVideoElements(
      elements.primaryVideo,
      elements.secondaryVideo,
    )

    // 设置前景媒体元素
    this.foregroundPlayer.setMediaElements(
      elements.foregroundVideo,
      elements.foregroundImage,
    )

    // 设置音频元素
    if (elements.ttsAudio || elements.bgmAudio) {
      this.audioManager.setAudioElements(elements.ttsAudio, elements.bgmAudio)
    }
  }

  /**
   * 开始直播背景循环
   */
  async startLiveBackground(videoGroup: MediaResource[]): Promise<void> {
    // 预加载视频资源
    await this.resourceManager.preloadResources(videoGroup)

    // 开始直播背景
    await this.liveBackgroundManager.startLiveBackground(videoGroup)

    // 添加事件到状态管理器
    this.mediaStateManager.addEvent({
      type: 'start_live_background',
      priority: 2, // NORMAL priority
      mediaType: 'live_background',
      data: { videoGroup },
    })
  }

  /**
   * 停止直播背景
   */
  stopLiveBackground(): void {
    this.liveBackgroundManager.stopLiveBackground()

    this.mediaStateManager.addEvent({
      type: 'stop_live_background',
      priority: 2, // NORMAL priority
      mediaType: 'live_background',
      data: {},
    })
  }

  /**
   * 播放前景视频
   */
  async playForegroundVideo(
    resource: MediaResource,
    config?: any,
  ): Promise<void> {
    // 预加载资源
    await this.resourceManager.preloadResource(resource)

    // 获取优化后的资源URL
    const optimizedUrl = await this.resourceManager.getResource(resource)
    const optimizedResource = { ...resource, url: optimizedUrl }

    // 播放前景视频
    await this.foregroundPlayer.playForegroundVideo(optimizedResource, config)

    // 添加事件到状态管理器
    this.mediaStateManager.addEvent({
      type: 'play_foreground_video',
      priority: 1, // HIGH priority
      mediaType: 'foreground_video',
      data: { resource: optimizedResource, config },
    })
  }

  /**
   * 显示前景图片
   */
  async showForegroundImage(
    resource: MediaResource,
    config?: any,
  ): Promise<void> {
    // 预加载资源
    await this.resourceManager.preloadResource(resource)

    // 获取优化后的资源URL
    const optimizedUrl = await this.resourceManager.getResource(resource)
    const optimizedResource = { ...resource, url: optimizedUrl }

    // 显示前景图片
    await this.foregroundPlayer.showForegroundImage(optimizedResource, config)

    // 添加事件到状态管理器
    this.mediaStateManager.addEvent({
      type: 'show_foreground_image',
      priority: 1, // HIGH priority
      mediaType: 'foreground_image',
      data: { resource: optimizedResource, config },
    })
  }

  /**
   * 跳过前景媒体
   */
  async skipForeground(): Promise<void> {
    await this.foregroundPlayer.skip()
  }

  /**
   * 播放TTS
   */
  async playTTS(message: string, audioUrl: string): Promise<void> {
    await this.audioManager.playTTS(message, audioUrl)

    this.mediaStateManager.addEvent({
      type: 'play_tts',
      priority: 1, // HIGH priority
      mediaType: 'audio_tts',
      data: { message, audioUrl },
    })
  }

  /**
   * 停止TTS
   */
  stopTTS(): void {
    this.audioManager.stopTTS()
  }

  /**
   * 播放BGM
   */
  async playBGM(url?: string): Promise<void> {
    await this.audioManager.playBGM(url)

    this.mediaStateManager.addEvent({
      type: 'play_bgm',
      priority: 3, // LOW priority
      mediaType: 'audio_bgm',
      data: { url },
    })
  }

  /**
   * 停止BGM
   */
  stopBGM(): void {
    this.audioManager.stopBGM()
  }

  /**
   * 设置音量
   */
  setVolume(type: 'tts' | 'bgm' | 'video', volume: number): void {
    this.audioManager.setVolume(type, volume)
  }

  /**
   * 静音/取消静音
   */
  setMuted(type: 'tts' | 'bgm' | 'video', muted: boolean): void {
    this.audioManager.setMuted(type, muted)
  }

  /**
   * 预加载资源
   */
  async preloadResources(resources: MediaResource[]): Promise<void> {
    await this.resourceManager.preloadResources(resources)
  }

  /**
   * 清除资源缓存
   */
  clearResourceCache(): void {
    this.resourceManager.clearCache()
  }

  /**
   * 获取当前状态
   */
  get currentState() {
    return {
      media: this.mediaStateManager.currentState,
      audio: this.audioManager.currentState,
      foreground: this.foregroundPlayer.status,
      cache: this.resourceManager.cacheStats,
    }
  }

  /**
   * 获取状态的响应式引用
   */
  get stateRefs() {
    return {
      media: this.mediaStateManager.stateRef,
      audio: this.audioManager.stateRef,
      cache: this.resourceManager.statsRef,
    }
  }

  /**
   * 销毁系统
   */
  destroy(): void {
    this.mediaStateManager.destroy()
    this.liveBackgroundManager.destroy()
    this.foregroundPlayer.destroy()
    this.audioManager.destroy()
    this.resourceManager.destroy()

    if (this.config.debugMode) {
      console.log('[VIDEO-GROUP-MSG] 🔥 UnifiedMediaSystem destroyed')
    }
  }
}

// 导出所有类型和组件
export * from './types'
export { MediaStateManager } from './MediaStateManager'
export { LiveStreamBackgroundManager } from './LiveStreamBackgroundManager'
export { ForegroundMediaPlayer } from './ForegroundMediaPlayer'
export { AudioStateManager } from './AudioStateManager'
export { ResourceManager } from './ResourceManager'

// 默认导出统一系统
export default UnifiedMediaSystem
