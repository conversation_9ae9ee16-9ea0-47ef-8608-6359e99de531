<template>
  <MobileLayout>
    <div class="mobile-home-page">
      <!-- 移动端Header -->
      <header class="app-header">
        <div class="logo">
          <NuxtImg
            :src="logoUrl"
            :alt="websiteTitle"
            loading="eager"
            width="117"
            height="24"
          />
        </div>
        <div class="header-buttons">
          <!-- 主题切换按钮 -->
          <button class="theme-toggle" @click="toggleTheme">
            <div class="toggle-track" :class="{ active: !isDarkTheme }">
              <div class="toggle-thumb" :class="{ active: !isDarkTheme }">
                <Icon
                  :name="isDarkTheme ? 'lucide:moon' : 'lucide:sun'"
                  class="theme-icon"
                />
              </div>
            </div>
          </button>

          <!-- 签到按钮 - 根据用户状态显示不同行为 -->
          <button class="checkin-btn" @click="handleCheckinClick">
            <!-- 有奖励可领取时显示摇摆的emoji -->
            <span :class="{ 'shake-animation': hasRewards }">🎁</span>
          </button>

          <a
            v-if="appName === 'ReelPlay'"
            href="https://x.com/Reelplay197835"
            target="_blank"
            rel="noopener noreferrer"
            class="btn"
          >
            <Icon name="lucide:twitter" size="16" />
            <span>Follow</span>
          </a>

          <a
            v-if="appName === 'Playshot'"
            href="https://t.me/+bnRxPQGjVPM5MzM1"
            target="_blank"
            rel="noopener noreferrer"
            class="btn"
          >
            <Icon name="lucide:send" size="16" />
            <span>Join</span>
          </a>

          <!-- 登录按钮或用户信息 -->
          <div
            class="auth-section"
            :class="{ 'auth-initialized': isInitialized }"
          >
            <button
              v-if="!userStore.isLoggedIn || userStore.isGuest"
              class="btn btn-primary auth-signin-btn"
              @click="showAuthModal"
            >
              Sign in
            </button>
            <CreditDisplay
              v-else
              :amount="userStore.userCoins"
              :show-add-button="true"
              class="auth-user-info"
              @add="handleRecharge"
            />
          </div>
        </div>
      </header>

      <!-- SEO 隐藏标题 - 上部区域 -->
      <h1 class="seo-hidden"
        >Free Best Pc & Mobile Otome Games, Romance Visual Novels</h1
      >

      <!-- 移动端主要内容 -->
      <main class="main-content">
        <section class="section">
          <div class="section-header">
            <div class="section-title">
              <span class="icon">🔥</span>
              Hottest
            </div>
            <p class="section-subtitle">Most popular stories for you</p>
          </div>

          <div class="filter-buttons">
            <button class="filter-btn" @click="showPopularDrawer = true">
              {{ selectedPopularLabel() }}
              <Icon name="lucide:chevron-down" size="12" />
            </button>
            <button
              :class="{
                'filter-btn': true,
                'tags-btn': selectedTags.length > 0 && !isLoading,
                'loading': isLoading,
              }"
              @click="showTagsDrawer = true"
            >
              {{ tagsButtonText() }}
            </button>
          </div>

          <!-- SEO 隐藏标题 - 中部区域 -->
          <h2 class="seo-hidden"
            >Play anime dating sim, Virtual Date Games Starting at $0</h2
          >

          <div class="story-grid-container">
            <!-- 加载状态：显示骨架屏 -->
            <template v-if="!isInitialized || storyStore.loading || isLoading">
              <div class="story-row-mobile">
                <StoryCard
                  v-for="n in 6"
                  :key="`skeleton-${n}`"
                  :story="{ id: `skeleton-${n}`, title: '', status: 'normal' }"
                  :is-pc="false"
                  :loading="true"
                />
              </div>
            </template>
            <!-- 空状态：已初始化但没有数据 -->
            <template v-else-if="displayStories.length === 0">
              <div class="no-stories">
                <p>No characters available under current conditions.</p>
              </div>
            </template>
            <!-- 正常数据显示 -->
            <template v-else>
              <div class="story-row-mobile">
                <StoryCard
                  v-for="story in displayStories"
                  :key="story.id"
                  :story="story"
                  :is-pc="false"
                  @click="handleStoryClick"
                  @subscription-change="handleSubscriptionChange"
                  @need-login="showAuthModal"
                  @need-email="showAuthModal"
                />
              </div>
            </template>
          </div>

          <!-- SEO 隐藏标题 - 中部区域 -->
          <h2 class="seo-hidden"
            >choose your own romance & online Romance Stories</h2
          >
        </section>
      </main>

      <!-- SEO 隐藏标题 - 底部区域 -->
      <h3 class="seo-hidden">interactive story-driven romance games</h3>
      <h3 class="seo-hidden"
        >Premium otome games English patch & anime art style</h3
      >

      <!-- 移动端Footer -->
      <footer class="footer">
        <a
          :href="`mailto:${brandingConfig.supportEmail}`"
          class="support-email"
        >
          Support Email: {{ brandingConfig.supportEmail }}
        </a>
      </footer>

      <!-- 模态框 -->
      <AuthModal
        v-if="showAuth"
        @close="showAuth = false"
        @login="handleLoginSuccess"
      />

      <!-- Category Drawers -->
      <CategoryDrawer
        :visible="showPopularDrawer"
        type="popular"
        :initial-popular="selectedPopular"
        @update:visible="showPopularDrawer = $event"
        @popular-change="handlePopularChange"
      />

      <CategoryDrawer
        :visible="showTagsDrawer"
        type="tags"
        title="Tags"
        :initial-tags="selectedTags"
        @update:visible="showTagsDrawer = $event"
        @tags-change="handleTagsChange"
      />
    </div>
  </MobileLayout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'

// 运行时配置
const config = useRuntimeConfig()
const { brandingConfig } = useBranding()
const logoUrl =
  config.public?.logoUrl ||
  'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
const websiteTitle = config.public?.websiteTitle || 'ReelPlay'
const appName = config.public?.appName || 'ReelPlay'

// 状态管理
const router = useRouter()
const userStore = useUserStore()
const storyStore = useStoryStore()
const checkinStore = useCheckinStore()
const tasksStore = useTasksStore()

// 使用标签筛选composable
const {
  selectedPopular,
  selectedTags,
  selectedPopularLabel,
  tagsButtonText,
  handlePopularChange,
  handleTagsChange,
  initFromUrlParams,
  isLoading,
} = useTagsFilter()

// 响应式数据
const isDarkTheme = ref(true)
const showAuth = ref(false)
const showPopularDrawer = ref(false)
const showTagsDrawer = ref(false)

// 立即初始化用户状态，避免闪现（与 PCHeader 保持一致的双重保险）
if (import.meta.client) {
  userStore.initFromStorage()

  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    isDarkTheme.value = savedTheme === 'dark'
  }

  // 延迟一帧后标记为已初始化，确保用户状态已经同步到 UI
  requestAnimationFrame(() => {
    isInitialized.value = true
  })
}

// 计算属性
const displayStories = computed(() => {
  // 确保返回数组，避免初始化问题
  const stories = storyStore.stories || []

  // 确保每个故事都有必需的字段
  return stories.map((story) => ({
    ...story,
    status: story.status || 'normal', // 提供默认值
  }))
})

// 初始化状态
const isInitialized = ref(false)

// 检查是否有可领取的奖励
const hasRewards = computed(() => {
  return checkinStore.canShowCheckin && !checkinStore.todayClaimed
})

// 方法
const toggleTheme = () => {
  isDarkTheme.value = !isDarkTheme.value
  if (import.meta.client) {
    localStorage.setItem('theme', isDarkTheme.value ? 'dark' : 'light')
    document.body.classList.toggle('light-theme', !isDarkTheme.value)
  }
}

const showAuthModal = () => {
  // 在移动端跳转到登录页面，而不是显示模态框
  if (import.meta.client && window.innerWidth <= 768) {
    router.push('/user/login')
  } else {
    showAuth.value = true
  }
}

const handleShowCheckin = () => {
  checkinStore.showModal()
}

const handleLoginSuccess = async () => {
  showAuth.value = false

  // 登录成功后获取签到信息和任务列表
  if (!userStore.isGuest) {
    await Promise.all([
      checkinStore.fetchCheckinInfo(),
      tasksStore.fetchTasks(),
    ])

    // 如果有可领取的奖励，显示签到弹窗
    if (hasRewards.value) {
      handleShowCheckin()
    }
  }
}

const handleRecharge = () => {
  const rechargeStore = useRechargeStore()
  rechargeStore.showRechargeModal()
}

const handleCheckinClick = () => {
  // 如果用户未登录，跳转到登录
  if (!userStore.isLoggedIn || userStore.isGuest) {
    showAuthModal()
    return
  }

  // 如果用户已登录，跳转到签到页面
  router.push('/daily-tasks')
}

// StoryCard相关事件处理
const handleStoryClick = (story) => {
  // 这里可以打开故事详情模态框或导航到故事页面
  navigateTo(`/story/${story.id}`)
}

const handleSubscriptionChange = (_story) => {
  // 这里可以触发数据重新获取或者通知父组件更新
}

// 客户端初始化
onMounted(async () => {
  try {
    // 用户状态初始化由 smart-auth.client.ts 插件处理，这里不重复调用

    // 初始化标签筛选（这会触发数据加载）
    await initFromUrlParams()

    // 如果没有故事数据，加载默认故事列表
    if (storyStore.stories.length === 0 && userStore.isLoggedIn) {
      await storyStore.fetchStories()
    }

    // 如果用户已登录且不是游客，获取签到信息
    if (userStore.isLoggedIn && !userStore.isGuest) {
      try {
        await checkinStore.fetchCheckinInfo()
      } catch (error) {
        console.error('Failed to fetch checkin info:', error)
      }
    }
  } catch (error) {
    console.error('Failed to initialize mobile home page:', error)
  } finally {
    // 标记为已初始化，无论成功还是失败
    isInitialized.value = true
  }
})

// 监听用户状态变化，当用户状态更新时执行相关操作
watch(
  () => userStore.isLoggedIn,
  async (newValue, oldValue) => {
    // 避免初始化时的触发
    if (oldValue === undefined) return

    if (newValue && !userStore.isGuest) {
      // 用户登录后，获取签到信息
      try {
        await checkinStore.fetchCheckinInfo()
      } catch (error) {
        console.error('Failed to fetch checkin info after login:', error)
      }
    }
  },
)
</script>

<style scoped>
/* 认证状态防闪现样式 */
.auth-section {
  /* 默认隐藏，避免闪现 */
  opacity: 0;
  transition: opacity 0.2s ease;
  min-width: 80px; /* 保持布局稳定 */
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.auth-section.auth-initialized {
  /* 初始化完成后显示 */
  opacity: 1;
}

/* 确保按钮和用户信息有一致的高度 */
.auth-signin-btn,
.auth-user-info {
  min-height: 28px;
  display: flex;
  align-items: center;
}

.mobile-home-page {
  height: 100%;
  background-color: var(--mobile-bg-primary);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  transition: background-color 0.3s ease;
}

/* 签到按钮样式 */
.checkin-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 44px;
  height: 36px;
}

.checkin-btn:hover {
  background: var(--bg-hover);
  border-color: var(--accent-color);
}

/* 摇摆动画 */
.shake-animation {
  animation: shake 0.8s ease-in-out infinite;
}

@keyframes shake {
  0%,
  100% {
    transform: rotate(0deg);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: rotate(-10deg);
  }
  20%,
  40%,
  60%,
  80% {
    transform: rotate(10deg);
  }
}

/* 确保其他按钮样式保持一致 */
.btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 44px;
  height: 36px;
}

.btn:hover {
  background: var(--bg-hover);
  border-color: var(--accent-color);
}

.btn-primary {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.btn-primary:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
}

/* Filter buttons styles */
.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 0 0 16px;
  padding: 0;
  width: 100%;
}

.filter-btn {
  display: flex;
  height: 30px;
  padding: 10px 16px;
  justify-content: center;
  align-items: center;
  gap: 12px;
  border-radius: 36px;
  border: 1px solid var(--accent-color);
  color: var(--accent-color);
  font-size: 12px;
  font-weight: 700;
  background: transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
  margin-bottom: 4px;
  cursor: pointer;
}

.filter-btn svg {
  width: 12px;
  height: 6px;
}

.filter-btn:hover {
  background: rgba(202, 147, 242, 0.2);
  color: var(--accent-color);
}

.filter-btn:active {
  transform: scale(0.95);
}

.filter-btn.tags-btn {
  background: var(--accent-color);
  color: var(--bg-primary);
  border-color: var(--accent-color);
  box-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);
}

.filter-btn.tags-btn:hover {
  background: var(--accent-hover);
  color: var(--bg-primary);
}

.filter-btn.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* SEO 隐藏标题 - 对用户不可见但对搜索引擎可见 */
.seo-hidden {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
  margin: 0;
  padding: 0;
  border: 0;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
}

/* 小屏幕适配 */
@media screen and (max-width: 360px) {
  .filter-buttons {
    justify-content: space-between;
  }

  .filter-btn {
    flex-grow: 0;
    flex-shrink: 0;
  }
}

/* 空状态样式 */
.no-stories {
  text-align: center;
  padding: 40px 16px;
  color: var(--text-secondary, rgba(255, 255, 255, 0.6));
  font-size: 16px;
}

body.light-theme .no-stories {
  color: rgba(0, 0, 0, 0.6);
}
</style>
