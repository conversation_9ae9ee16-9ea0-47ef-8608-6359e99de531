/* CSS变量 - 暗色主题默认 */
:root {
  --bg-primary: #180430;
  --bg-secondary: #290e40;
  --bg-tertiary: rgb(255 255 255 / 5%);
  --bg-card: rgb(255 255 255 / 8%);
  --bg-hover: rgb(255 255 255 / 10%);
  --text-primary: #fff;
  --text-secondary: rgb(255 255 255 / 70%);
  --text-tertiary: rgb(255 255 255 / 50%);
  --border-color: rgb(255 255 255 / 10%);
  --divider-color: rgb(255 255 255 / 5%);
  --accent-color: #ca93f2;
  --accent-hover: #b87de0;
  --accent-bg: rgb(202 147 242 / 10%);
  --shadow-color: rgb(0 0 0 / 20%);
  --mobile-bg-primary: #180430;
  --mobile-bg-secondary: #290e40;
  --mobile-app-bg: #1f0038;
  --mobile-input-bg: rgba(255, 255, 255, 0.05);
  --mobile-input-border: rgba(255, 255, 255, 0.1);
  --mobile-menu-bg: #1f0038;
  --mobile-bg-gradient-start: #2b1b2f;
  --mobile-bg-gradient-end: #1a1021;

  /* PC端专用变量 */
  --sidebar-bg: #290e40;
  --header-bg: rgb(24 4 48 / 95%);
  --pc-top-header-bg: #2a1b42;
  --pc-sidebar-active-bg: rgb(202 147 242 / 20%);
  --pc-sidebar-hover-bg: rgb(255 255 255 / 10%);
  --pc-sidebar-active-text: #ca93f2;
  --pc-sidebar-text: rgb(255 255 255 / 70%);
  --pc-sidebar-icon-size: 20px;
  --pc-sidebar-item-radius: 20px;
  --pc-sidebar-item-padding: 15px 20px;

  /* 钻石数量颜色 */
  --coins-color: #daff96;
}

/* 亮色主题 */
body.light-theme {
  --bg-primary: #f8f9fa;
  --bg-secondary: #fff;
  --bg-tertiary: rgb(0 0 0 / 5%);
  --bg-card: #fff;
  --bg-hover: rgb(0 0 0 / 5%);
  --text-primary: #333;
  --text-secondary: #666;
  --text-tertiary: #999;
  --border-color: rgb(0 0 0 / 10%);
  --divider-color: rgb(0 0 0 / 5%);
  --accent-color: #ca93f2;
  --accent-hover: #7d3c98;
  --accent-bg: rgb(142 68 173 / 10%);
  --shadow-color: rgb(0 0 0 / 10%);
  --mobile-bg-primary: #f8f9fa;
  --mobile-bg-secondary: #fff;
  --mobile-app-bg: #ffffff;
  --mobile-input-bg: rgba(0, 0, 0, 0.05);
  --mobile-input-border: rgba(0, 0, 0, 0.1);
  --mobile-menu-bg: #ffffff;
  --mobile-bg-gradient-start: #f0f0f0;
  --mobile-bg-gradient-end: #ffffff;

  /* PC端专用变量 - 亮色主题 */
  --sidebar-bg: #fff;
  --header-bg: rgb(255 255 255 / 95%);
  --pc-top-header-bg: #fff;
  --pc-sidebar-active-bg: rgb(202 147 242 / 20%);
  --pc-sidebar-hover-bg: rgb(0 0 0 / 5%);
  --pc-sidebar-active-text: #ca93f2;
  --pc-sidebar-text: #666;

  /* 钻石数量颜色 - 亮色模式下使用更深的绿色 */
  --coins-color: #2d7d32;
}

/* 基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  /* 禁止移动端双击缩放 */
  touch-action: manipulation;
}

body {
  min-height: calc(var(--vh, 1vh) * 100);
  color: var(--text-primary);
  font-family:
    'Work Sans',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  line-height: 1.6;
  background-color: var(--mobile-bg-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
  /* 禁止移动端双击缩放 */
  touch-action: manipulation;
  -webkit-user-select: none;
  user-select: none;
}

/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from,
.page-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

/* 布局样式 */
.layout-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden auto;
  background-color: var(--mobile-bg-primary);
  transition: background-color 0.3s ease;
  /* 禁止移动端双击缩放 */
  touch-action: manipulation;
}

/* App Header样式 - 用于主页等需要space-between布局的header */
.app-header {
  position: relative;
  top: 0;
  right: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px 0;
  background-color: transparent;
  min-height: 48px;
}

.logo {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.logo img {
  width: 117px;
  height: 24px;
  object-fit: contain;
}

.header-buttons {
  display: flex;
  flex-wrap: nowrap;
  gap: 6px;
  align-items: center;
  justify-content: flex-end;
  min-width: 0;
  flex-shrink: 0;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  gap: 4px;
  align-items: center;
  padding: 6px 12px;
  color: var(--text-primary);
  font-size: 13px;
  font-weight: 500;
  text-decoration: none;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-height: 32px;
  box-sizing: border-box;
}

.btn:hover {
  background: var(--bg-hover);
  transform: translateY(-1px);
}

.btn-primary {
  color: white;
  background: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-primary:hover {
  background: var(--accent-hover);
}

/* 主题切换按钮 */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  background: none;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 32px;
}

.toggle-track {
  position: relative;
  width: 42px;
  height: 22px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 11px;
  transition: all 0.3s ease;
}

.toggle-thumb {
  position: absolute;
  top: 1px;
  left: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  background: var(--text-primary);
  border-radius: 50%;
  box-shadow: 0 1px 3px var(--shadow-color);
  transition: all 0.3s ease;
}

.toggle-thumb.active {
  transform: translateX(20px);
}

.theme-icon {
  width: 10px;
  height: 10px;
  color: var(--bg-primary);
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 2;
  flex: 1;
  width: 100%;
  margin-top: 0;
  margin-bottom: 20px;
  padding: 0 16px 16px;
}

/* 故事相关样式 */
.section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 0;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 18px;
}

.section-subtitle {
  margin-top: 2px;
  color: var(--text-secondary);
  font-size: 12px;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
  margin: 0 0 16px;
  padding: 0;
}

.filter-btn {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 8px 16px;
  color: var(--text-primary);
  font-size: 14px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: var(--bg-hover);
}

/* Story Grid Container */
.story-grid-container {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  height: auto;
  min-height: 300px;
  overflow: visible;
}

/* Story Row Mobile - 与CSR完全一致 */
.story-row-mobile {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  box-sizing: border-box;
  width: 100%;
  padding: 0;
}

/* Story Card Item */
.story-card-item {
  width: 100%;
  height: auto;
}

/* Story Card - 与CSR完全一致 */
.story-card {
  position: relative;
  width: 100%;
  overflow: hidden;
  background: #1f0038;
  border-radius: 16px;
  transform: translateZ(0);
  cursor: pointer;
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
  aspect-ratio: 167/294;
  contain: layout style paint;
}

.story-card:hover {
  transform: translateZ(0) translateY(-2px);
}

/* Story Image */
.story-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background: #140a29;
  background-image: linear-gradient(234deg, #1f0038 0%, #140a29 100%);
  pointer-events: none;
}

.story-media {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: translateZ(0);
  backface-visibility: hidden;
  transition: opacity 0.3s ease;
}

/* Story Badge */
.story-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  padding: 4px 8px;
  color: white;
  font-weight: 600;
  font-size: 10px;
  background: var(--accent-color);
  border-radius: 12px;
}

/* Story Overlay */
.story-overlay {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: flex-end;
  padding: 16px 12px 6px;
  color: white;
  background: linear-gradient(
    180deg,
    rgb(31 0 56 / 0%) 0%,
    rgb(31 0 56 / 70%) 42.54%
  );
  transition: opacity 0.5s ease;
}

.story-name {
  display: -webkit-box;
  margin-bottom: 4px;
  overflow: hidden;
  font-weight: 600;
  font-size: 12px;
  line-height: 1.2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.story-count {
  margin-bottom: 8px;
  font-size: 10px;
  opacity: 0.8;
}

.story-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgb(255 255 255 / 20%);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.story-button img {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.story-card:hover .story-button img {
  transform: scale(1.1);
}

/* Footer */
.footer {
  position: relative;
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 20px 16px;
  background-color: var(--mobile-bg-primary);
}

.support-email {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 11px;
  text-align: center;
  text-decoration: none;
  opacity: 0.5;
}

.support-email:hover {
  opacity: 0.8;
}

/* 响应式 */
@media screen and (max-width: 480px) {
  .app-header {
    padding: 10px 12px 0;
  }

  .header-buttons {
    gap: 2px;
  }

  .btn {
    padding: 5px 10px;
    font-size: 12px;
    min-height: 30px;
  }

  .toggle-track {
    width: 38px;
    height: 20px;
  }

  .toggle-thumb {
    width: 16px;
    height: 16px;
  }

  .toggle-thumb.active {
    transform: translateX(18px);
  }

  .theme-icon {
    width: 9px;
    height: 9px;
  }

  .story-row-mobile {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 360px) {
  .app-header {
    padding: 8px 10px 0;
  }

  .header-buttons {
    gap: 3px;
  }

  .btn {
    padding: 4px 8px;
    font-size: 11px;
    min-height: 28px;
  }

  .btn span {
    display: none;
  }

  .toggle-track {
    width: 36px;
    height: 18px;
  }

  .toggle-thumb {
    width: 14px;
    height: 14px;
  }

  .toggle-thumb.active {
    transform: translateX(16px);
  }

  .story-row-mobile {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (min-width: 768px) {
  .main-content {
    padding: 16px 32px;
  }
}

/* PC端布局 - 参考CSR项目PCLayout */
.pc-layout {
  display: flex;
  flex-direction: column;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

/* PC端顶部导航栏 */
.top-header {
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
  padding: 0 40px 0 20px;
  background-color: var(--pc-top-header-bg);
  border-bottom: 1px solid var(--divider-color);
  box-shadow: 0 2px 10px rgb(0 0 0 / 5%);
  backdrop-filter: blur(10px);
}

/* PC端Logo */
.top-header .logo {
  display: flex;
  gap: 8px;
  align-items: center;
  cursor: pointer;
}

.top-header .logo img {
  width: 142px;
  height: 31px;
  object-fit: contain;
}

/* PC端用户操作区域 */
.user-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 社交链接按钮 */
.x-btn,
.telegram-btn {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 16px;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
  text-decoration: none;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.x-btn:hover,
.telegram-btn:hover {
  background: var(--bg-hover);
  transform: translateY(-1px);
}

/* 登录按钮 */
.sign-in-btn {
  padding: 10px 20px;
  color: white;
  font-weight: 600;
  font-size: 14px;
  background: var(--accent-color);
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sign-in-btn:hover {
  background: var(--accent-hover);
  box-shadow: 0 4px 12px rgb(202 147 242 / 30%);
  transform: translateY(-1px);
}

/* 用户信息容器 */
.user-info-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.user-avatar-wrapper {
  cursor: pointer;
}

.user-avatar {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 50%;
}

/* PC端钻石数量显示 */
.coins-display {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 8px 12px;
  color: var(--coins-color);
  font-weight: 600;
  font-size: 14px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.coins-display:hover {
  background: var(--bg-hover);
  transform: translateY(-1px);
}

.coins-display .icon {
  font-size: 16px;
}

/* PC端主题切换按钮 */
.theme-toggle-pc {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--text-primary);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-toggle-pc:hover {
  background: var(--bg-hover);
  transform: scale(1.05);
}

/* PC端社交链接 */
.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.social-link:hover {
  color: var(--text-primary);
  background: var(--bg-hover);
  transform: scale(1.05);
}

/* PC端登录按钮 */
.btn-primary-pc {
  padding: 10px 20px;
  color: white;
  font-weight: 600;
  font-size: 14px;
  background: var(--accent-color);
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary-pc:hover {
  background: var(--accent-hover);
  box-shadow: 0 4px 12px rgb(202 147 242 / 30%);
  transform: translateY(-1px);
}

/* PC端主要内容区域 */
.pc-main {
  flex: 1;
  overflow: hidden auto;
  background-color: var(--bg-primary);
}

/* PC端容器 */
.pc-container {
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 0 24px;
}

/* PC端分割线 */
.pc-divider {
  height: 1px;
  margin: 24px 0;
  background: var(--divider-color);
}

/* 内容容器 */
.content-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧导航栏 */
.sidebar {
  display: flex;
  flex-direction: column;
  width: 240px;
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--divider-color);
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 80px;
}

/* 导航菜单 */
.nav-menu {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  gap: 12px;
  align-items: center;
  margin: 4px 12px;
  padding: 12px 20px;
  color: var(--pc-sidebar-text);
  font-weight: 500;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background: var(--pc-sidebar-hover-bg);
}

.nav-item.active {
  color: var(--pc-sidebar-active-text);
  background: var(--pc-sidebar-active-bg);
}

.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 12px;
}

/* 侧边栏底部 */
.sidebar-bottom {
  padding: 20px;
  border-top: 1px solid var(--divider-color);
}

.daily-reward-btn {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  margin-bottom: 12px;
  padding: 12px;
  color: var(--text-primary);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.daily-reward-btn:hover {
  background: var(--bg-hover);
  transform: translateY(-1px);
}

.sidebar.collapsed .daily-reward-btn {
  justify-content: center;
}

.discord-btn {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  margin-bottom: 12px;
  padding: 12px;
  color: white;
  font-weight: 500;
  text-decoration: none;
  background: #5865f2;
  border: none;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.discord-btn:hover {
  background: #4752c4;
  transform: translateY(-1px);
}

.sidebar.collapsed .discord-btn {
  justify-content: center;
}

.sidebar-toggle {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  padding: 12px;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  color: var(--text-primary);
  background: var(--bg-hover);
}

.sidebar.collapsed .sidebar-toggle {
  /* justify-content: center; */
}

.rotate-180 {
  transform: rotate(180deg);
}

/* PC端主内容区域 */
.pc-layout .main-content {
  flex: 1;
  overflow: hidden auto;
  background-color: var(--bg-primary);
}

/* PC端故事网格布局 - 参考CSR项目VirtualStoryGrid */
.stories-grid-pc {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 30px;
  margin-top: 24px;
  padding: 0 24px;
}

/* 响应式调整 - 与CSR项目保持一致 */
@media screen and (max-width: 1600px) {
  .stories-grid-pc {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 25px;
    padding: 0 20px;
  }
}

@media screen and (max-width: 1400px) {
  .stories-grid-pc {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 20px;
    padding: 0 18px;
  }
}

@media screen and (max-width: 1200px) {
  .stories-grid-pc {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 18px;
    padding: 0 16px;
  }
}

@media screen and (max-width: 768px) {
  .stories-grid-pc {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
    padding: 0 14px;
  }
}

/* PC端section样式 */
.stories-section {
  padding: 40px 0;
}

.pc-layout .section-header {
  margin-bottom: 32px;
  text-align: center;
}

.pc-layout .section-title {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 24px;
}

.pc-layout .section-subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 16px;
}

.section-actions {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.btn-secondary-pc {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 12px 24px;
  color: var(--text-primary);
  font-weight: 600;
  text-decoration: none;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary-pc:hover {
  background: var(--bg-hover);
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-1px);
}
